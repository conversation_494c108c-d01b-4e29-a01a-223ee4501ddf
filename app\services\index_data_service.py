"""
Index Data Service

This module handles fetching and storing market index data from the Colombo Stock Exchange API.
It provides functionality for retrieving ASPI and S&PSL20 index data.
"""

import logging
import asyncio
import random
from datetime import datetime
from typing import Dict, Any, Optional, TypedDict
from decimal import Decimal
import httpx
from bson.decimal128 import Decimal128
from fastapi import HTT<PERSON>Exception

from app.db.session import get_database
from app.utils.db_utils import safe_mongodb_operation
from app.utils.date_utils import skip_if_weekend
from app.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# API endpoints from settings
ASPI_DATA_API_URL = settings.CSE_API_ASPI_DATA_URL
SNPSL_DATA_API_URL = settings.CSE_API_SNPSL_DATA_URL

# Define types for cache
class CacheData(TypedDict):
    data: Optional[Dict[str, Any]]
    timestamp: Optional[datetime]

# Cache for index data to reduce API calls
_aspi_data_cache: CacheData = {"data": None, "timestamp": None}
_snpsl_data_cache: CacheData = {"data": None, "timestamp": None}

# Cache expiry time in seconds (5 minutes)
CACHE_EXPIRY = 300

async def fetch_with_retry(client: httpx.AsyncClient, url: str, json_data: Optional[Dict[str, Any]] = None,
                        max_retries: int = 3, base_delay: float = 1.0) -> Dict:
    """Fetch data with exponential backoff retry logic.

    Args:
        client: httpx AsyncClient instance
        url: API endpoint URL
        json_data: JSON data for POST request
        max_retries: Maximum number of retry attempts
        base_delay: Base delay for exponential backoff in seconds

    Returns:
        API response as dictionary

    Raises:
        Exception: If all retry attempts fail
    """
    retries = 0
    last_exception = None

    while retries <= max_retries:
        try:
            # Set content type header
            headers = {
                "Content-Type": "application/json"
            }

            # Make POST request with empty payload if none provided
            if json_data is None:
                json_data = {}

            response = await client.post(url, json=json_data, headers=headers)

            # Define retriable status codes
            retriable_status_codes = {
                429,  # Too Many Requests
                502,  # Bad Gateway
                503,  # Service Unavailable
                504   # Gateway Timeout
            }

            if response.status_code == 200:
                return response.json()
            elif response.status_code in retriable_status_codes:
                # Transient error, retry with exponential backoff
                delay = base_delay * (2 ** retries) + (0.1 * random.random())
                logger.warning("Transient HTTP error %d, retrying in %.2f seconds...",
                              response.status_code, delay)
                await asyncio.sleep(delay)
                retries += 1
                continue
            else:
                # Other non-retriable error status codes
                error_msg = f"API request failed with status {response.status_code}: {response.text}"
                logger.error(error_msg)
                last_exception = Exception(error_msg)
                break

        except httpx.RequestError as e:
            retries += 1
            if retries <= max_retries:
                delay = base_delay * (2 ** retries) + (0.1 * random.random())
                logger.warning("Request error, retrying in %.2f seconds: %s", delay, str(e))
                await asyncio.sleep(delay)
            else:
                logger.error("Request failed after %d retries: %s", max_retries, str(e))
                last_exception = e
                break

    if last_exception:
        raise last_exception

    raise Exception("Failed to fetch data after multiple retries")

async def fetch_aspi_data() -> Dict[str, Any]:
    """Fetch ASPI index data from CSE API.

    Returns:
        Dictionary containing ASPI index data
    """
    current_time = datetime.now()

    # Check if we have valid cached data
    if (_aspi_data_cache["data"] is not None and
        _aspi_data_cache["timestamp"] is not None and
        (current_time - _aspi_data_cache["timestamp"]).total_seconds() < CACHE_EXPIRY):
        logger.debug("Using cached ASPI data")
        return _aspi_data_cache["data"]

    # If no valid cache, fetch from API
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info("Fetching ASPI data from CSE API")
            response = await fetch_with_retry(client, ASPI_DATA_API_URL)

            # Update cache
            _aspi_data_cache["data"] = response
            _aspi_data_cache["timestamp"] = current_time

            return response
    except Exception as e:
        logger.error("Error fetching ASPI data: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch ASPI data from CSE: {str(e)}"
        )

async def fetch_snpsl_data() -> Dict[str, Any]:
    """Fetch S&PSL20 index data from CSE API.

    Returns:
        Dictionary containing S&PSL20 index data
    """
    current_time = datetime.now()

    # Check if we have valid cached data
    if (_snpsl_data_cache["data"] is not None and
        _snpsl_data_cache["timestamp"] is not None and
        (current_time - _snpsl_data_cache["timestamp"]).total_seconds() < CACHE_EXPIRY):
        logger.debug("Using cached S&PSL20 data")
        return _snpsl_data_cache["data"]

    # If no valid cache, fetch from API
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            logger.info("Fetching S&PSL20 data from CSE API")
            response = await fetch_with_retry(client, SNPSL_DATA_API_URL)

            # Update cache
            _snpsl_data_cache["data"] = response
            _snpsl_data_cache["timestamp"] = current_time

            return response
    except Exception as e:
        logger.error("Error fetching S&PSL20 data: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch S&PSL20 data from CSE: {str(e)}"
        )

async def store_index_data(index_name: str, index_data: Dict[str, Any]) -> bool:
    """Store index data in the database.

    Args:
        index_name: Name of the index (ASPI or S&PSL20)
        index_data: Index data from CSE API

    Returns:
        True if successful, False otherwise
    """
    try:
        # Get the database for the current event loop
        db = await get_database()

        # Validate input data
        if not index_data or "timestamp" not in index_data:
            logger.error(f"Invalid index data for {index_name}: missing timestamp")
            return False

        # Prepare data for storage
        timestamp = datetime.fromtimestamp(index_data.get("timestamp", 0) / 1000)  # Convert milliseconds to seconds
        date_str = timestamp.date().isoformat()

        logger.debug(f"Attempting to store {index_name} data for {date_str}")

        # Check if data already exists for this date and index
        existing_data = await safe_mongodb_operation(
            lambda: db["historical_index_data"].find_one({
                "dateStr": date_str,
                "indexName": index_name
            })
        )

        if existing_data:
            logger.info(f"Skipping {index_name} data for {date_str} as it already exists")
            return True

        # Create document for new data
        document = {
            "dateStr": date_str,  # Store date as string for querying
            "date": timestamp.replace(hour=0, minute=0, second=0, microsecond=0),  # Store date as datetime for MongoDB
            "timestamp": timestamp,
            "indexName": index_name,
            "value": Decimal128(str(Decimal(str(index_data.get("value", 0))))),
            "change": Decimal128(str(Decimal(str(index_data.get("change", 0))))),
            "percentageChange": Decimal128(str(Decimal(str(index_data.get("percentage", 0))))),  # Already in percentage format
            "lowValue": Decimal128(str(Decimal(str(index_data.get("lowValue", 0))))),
            "highValue": Decimal128(str(Decimal(str(index_data.get("highValue", 0))))),
            "rawData": index_data  # Store the raw data for reference
        }

        logger.debug(f"Inserting {index_name} document: {document}")

        # Insert new data
        result = await safe_mongodb_operation(
            lambda: db["historical_index_data"].insert_one(document)
        )

        if result and hasattr(result, 'inserted_id'):
            logger.info(f"Successfully stored new {index_name} data for {date_str} with ID: {result.inserted_id}")
            return True
        else:
            logger.error(f"Failed to insert {index_name} data for {date_str}: result was {result}")
            return False

    except Exception as e:
        logger.error(f"Error storing {index_name} data: {str(e)}", exc_info=True)
        return False

async def fetch_and_store_index_data(force: bool = False) -> Dict[str, Any]:
    """Fetch and store both ASPI and S&PSL20 index data.

    Args:
        force: If True, fetch data even on weekends

    Returns:
        Dictionary containing operation results
    """
    results = {
        "success": True,
        "aspi": {"success": False, "message": ""},
        "snpsl20": {"success": False, "message": ""}
    }

    try:
        # Check if today is a weekend
        current_date = datetime.now()
        weekend_skip = skip_if_weekend(current_date, "index data fetch", force=force)

        if weekend_skip:
            # Add service-specific fields to the standard weekend skip response
            weekend_skip.update({
                "aspi": {"success": True, "message": "Skipped (weekend)"},
                "snpsl20": {"success": True, "message": "Skipped (weekend)"}
            })
            return weekend_skip

        # Fetch and store ASPI data
        aspi_data = await fetch_aspi_data()
        aspi_result = await store_index_data("ASPI", aspi_data)
        results["aspi"] = {
            "success": aspi_result,
            "message": "Successfully stored ASPI data" if aspi_result else "Failed to store ASPI data"
        }

        # Fetch and store S&PSL20 data
        snpsl_data = await fetch_snpsl_data()
        snpsl_result = await store_index_data("S&PSL20", snpsl_data)
        results["snpsl20"] = {
            "success": snpsl_result,
            "message": "Successfully stored S&PSL20 data" if snpsl_result else "Failed to store S&PSL20 data"
        }

        # Overall success is true only if both operations succeeded
        results["success"] = aspi_result and snpsl_result
        results["message"] = "Successfully fetched and stored index data" if results["success"] else "Failed to fetch and store some index data"

        return results
    except Exception as e:
        logger.error(f"Error in fetch_and_store_index_data: {str(e)}")
        return {
            "success": False,
            "message": f"Error fetching and storing index data: {str(e)}",
            "aspi": {"success": False, "message": "Failed due to exception"},
            "snpsl20": {"success": False, "message": "Failed due to exception"}
        }
