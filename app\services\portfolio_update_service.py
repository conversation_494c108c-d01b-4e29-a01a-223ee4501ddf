"""
Portfolio Update Service

This module provides functionality to update portfolios with the latest market data.
It handles daily updates of portfolio market values and unrealized gains/losses.

The following fields are updated during the daily update process:
- At portfolio level: totalMarketValue, totalUnrealizedGainLoss, lastUpdatedDate
- At holding level: lastTradedPrice, marketValue, unrealizedGainLoss

Additionally, it logs portfolio values to the portfolio_value_history collection for benchmarking.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from decimal import Decimal, ROUND_HALF_UP
from bson.decimal128 import Decimal128
from zoneinfo import ZoneInfo

from app.db.repositories import TradeRepository, PortfolioRepository
from app.db.session import get_database
from app.utils.db_utils import safe_mongodb_operation
from app.utils.date_utils import skip_if_weekend

# Configure logging
logger = logging.getLogger(__name__)

async def log_portfolio_value_history(
    portfolio_id: str,
    total_market_value: Decimal128,
    update_date: datetime
) -> bool:
    """
    Log portfolio value to the portfolio_value_history collection for benchmarking.

    This function checks if a record already exists for the same portfolio and date
    before inserting to prevent duplicate records.

    Args:
        portfolio_id: ID of the portfolio
        total_market_value: Total market value of the portfolio
        update_date: Date of the update

    Returns:
        True if logging was successful or record already exists, False otherwise
    """
    try:
        # Get the database for the current event loop
        db = await get_database()

        # Normalize the date to remove time components for consistent comparison
        normalized_date = update_date.replace(hour=0, minute=0, second=0, microsecond=0)
        date_str = normalized_date.date().isoformat()

        # Check if a record already exists for this portfolio and date
        existing_record = await safe_mongodb_operation(
            lambda: db["portfolio_value_history"].find_one({
                "portfolioId": portfolio_id,
                "dateStr": date_str
            })
        )

        if existing_record:
            logger.debug(f"Portfolio value history already exists for portfolio {portfolio_id} on {date_str}, skipping insertion")
            return True

        # Create document for portfolio value history
        document = {
            "portfolioId": portfolio_id,  # This is the metaField for the time-series collection
            "dateStr": date_str,  # Store date as string for querying
            "date": normalized_date,  # Store date as datetime for MongoDB
            "timestamp": update_date,
            "totalMarketValue": total_market_value
        }

        # Insert new record since none exists for this date
        result = await safe_mongodb_operation(
            lambda: db["portfolio_value_history"].insert_one(document)
        )

        if result and result.inserted_id:
            logger.debug(f"Inserted new value history for portfolio {portfolio_id} on {date_str}")
            return True
        else:
            logger.warning(f"Failed to insert value history for portfolio {portfolio_id} on {date_str}")
            return False

    except Exception as e:
        logger.error(f"Error logging portfolio value history for {portfolio_id}: {str(e)}")
        return False

async def update_all_portfolios(force: bool = False) -> Dict[str, Any]:
    """
    Update all active portfolios with the latest market data.

    This function updates the following fields for all portfolios:
    - At portfolio level: totalMarketValue, totalUnrealizedGainLoss, lastUpdatedDate
    - At holding level: lastTradedPrice, marketValue, unrealizedGainLoss

    The function:
    1. Fetches all active portfolios
    2. Updates each portfolio with the latest market data
    3. Returns a summary of the update operation

    Args:
        force: If True, update portfolios even on weekends

    Returns:
        Dictionary containing update operation summary with standardized format
    """
    update_timestamp = datetime.now(ZoneInfo("Asia/Colombo"))

    # Check if today is a weekend
    weekend_skip = skip_if_weekend(update_timestamp, "portfolio updates", force=force)
    if weekend_skip:
        # Add service-specific fields to the standard weekend skip response
        weekend_skip.update({
            "portfolios_updated": 0,
            "portfolios_failed": 0
        })
        return weekend_skip

    try:
        # Get all portfolios
        portfolios = await PortfolioRepository.get_all_portfolios()

        if not portfolios:
            logger.info("No active portfolios found to update")
            return {
                "success": True,
                "message": "No active portfolios found to update",
                "portfolios_updated": 0,
                "portfolios_failed": 0,
                "timestamp": update_timestamp.isoformat()
            }

        # Track update statistics
        updated_count = 0
        failed_count = 0
        portfolio_updates = []
        total_holdings_updated = 0
        total_holdings_skipped = 0

        # Update each portfolio
        for portfolio in portfolios:
            try:
                portfolio_id = portfolio.get("portfolioId")
                if not portfolio_id:
                    logger.warning("Portfolio with missing ID detected")
                    failed_count += 1
                    portfolio_updates.append({
                        "portfolioId": "unknown",
                        "status": "failed",
                        "error": "Portfolio is missing ID field",
                        "error_code": "MISSING_PORTFOLIO_ID"
                    })
                    continue

                logger.info(f"Updating portfolio {portfolio_id}")
                result = await update_portfolio(portfolio)

                if result.get("success"):
                    updated_count += 1
                    # Track holdings statistics
                    holdings_updated = result.get("updatedHoldingsCount", 0)
                    holdings_skipped = result.get("skippedHoldingsCount", 0)
                    total_holdings_updated += holdings_updated
                    total_holdings_skipped += holdings_skipped

                    # Values are already converted to float in update_portfolio
                    update_info = {
                        "portfolioId": portfolio_id,
                        "status": "updated",
                        "newMarketValue": result.get("totalMarketValue"),
                        "totalUnrealizedGainLoss": result.get("totalUnrealizedGainLoss"),
                        "lastUpdatedDate": result.get("lastUpdatedDate"),
                        "holdingsUpdated": holdings_updated,
                        "holdingsSkipped": holdings_skipped
                    }

                    # Include any holdings with no data if present
                    if "holdingsWithNoData" in result:
                        update_info["holdingsWithNoData"] = result["holdingsWithNoData"]

                    portfolio_updates.append(update_info)
                else:
                    failed_count += 1
                    portfolio_updates.append({
                        "portfolioId": portfolio_id,
                        "status": "failed",
                        "error": result.get("message"),
                        "error_code": result.get("error_code", "UPDATE_FAILED")
                    })
            except Exception as e:
                failed_count += 1
                portfolio_id = portfolio.get("portfolioId", "unknown")
                logger.error(f"Error updating portfolio {portfolio_id}: {str(e)}", exc_info=True)
                portfolio_updates.append({
                    "portfolioId": portfolio_id,
                    "status": "failed",
                    "error": str(e),
                    "error_code": "UNEXPECTED_ERROR"
                })

        # Return summary with detailed statistics
        return {
            "success": True,
            "message": f"Updated {updated_count} portfolios, {failed_count} failed",
            "portfolios_updated": updated_count,
            "portfolios_failed": failed_count,
            "total_holdings_updated": total_holdings_updated,
            "total_holdings_skipped": total_holdings_skipped,
            "update_details": portfolio_updates,
            "timestamp": update_timestamp.isoformat()
        }

    except Exception as e:
        logger.error(f"Error in portfolio update process: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"Portfolio update process failed: {str(e)}",
            "error_code": "BATCH_UPDATE_FAILED",
            "portfolios_updated": 0,
            "portfolios_failed": 0,
            "timestamp": update_timestamp.isoformat()
        }

async def update_portfolio(portfolio: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update a specific portfolio with the latest market data.

    This function updates the following fields:
    - At portfolio level: totalMarketValue, totalUnrealizedGainLoss, lastUpdatedDate
    - At holding level: lastTradedPrice, marketValue, unrealizedGainLoss

    Args:
        portfolio: Portfolio document from the database

    Returns:
        Dictionary containing update operation result
    """
    portfolio_id = portfolio.get("portfolioId")
    holdings = portfolio.get("holdings", [])

    if not portfolio_id:
        logger.error("Cannot update portfolio: Missing portfolio ID")
        return {
            "success": False,
            "message": "Cannot update portfolio: Missing portfolio ID",
            "error_code": "MISSING_PORTFOLIO_ID"
        }

    if not holdings:
        logger.info(f"Portfolio {portfolio_id} has no holdings to update")
        # Return existing values if available, otherwise default to zero
        total_market_value = portfolio.get("totalMarketValue", Decimal128('0'))
        total_unrealized_gain_loss = portfolio.get("totalUnrealizedGainLoss", Decimal128('0'))

        return {
            "success": True,
            "message": f"Portfolio {portfolio_id} has no holdings to update",
            "portfolioId": portfolio_id,
            "totalMarketValue": float(total_market_value.to_decimal()),
            "totalUnrealizedGainLoss": float(total_unrealized_gain_loss.to_decimal()),
            "lastUpdatedDate": datetime.now(ZoneInfo("Asia/Colombo")).isoformat()
        }

    try:
        # Update each holding with latest price data
        updated_holdings = []
        total_market_value = Decimal128('0')
        unrealized_gain_loss_total = Decimal128('0')
        update_timestamp = datetime.now(ZoneInfo("Asia/Colombo"))

        # Track statistics for logging and response
        updated_holdings_count = 0
        skipped_holdings_count = 0
        holdings_with_no_data = []

        for holding in holdings:
            ticker_symbol = holding.get("tickerSymbol")
            quantity = holding.get("quantity")
            total_cost = holding.get("totalCost")

            if not ticker_symbol or not quantity or not total_cost:
                logger.warning(f"Skipping holding with missing data in portfolio {portfolio_id}: {holding}")
                # Keep the existing values for this holding
                updated_holdings.append(holding)
                skipped_holdings_count += 1

                # Add to totals using existing values
                market_value = holding.get("marketValue", Decimal128('0'))
                unrealized_gain_loss = holding.get("unrealizedGainLoss", Decimal128('0'))

                # Add to totals
                total_market_value = Decimal128(
                    str(total_market_value.to_decimal() + market_value.to_decimal())
                )
                unrealized_gain_loss_total = Decimal128(
                    str(unrealized_gain_loss_total.to_decimal() + unrealized_gain_loss.to_decimal())
                )
                continue

            # Get latest trade data for this ticker
            latest_trade = await TradeRepository.get_latest_trade(ticker_symbol)

            if not latest_trade:
                logger.warning(f"No latest trade data found for {ticker_symbol} in portfolio {portfolio_id}")
                holdings_with_no_data.append(ticker_symbol)
                # Keep the existing values for this holding
                updated_holdings.append(holding)
                skipped_holdings_count += 1

                # Add to totals using existing values
                market_value = holding.get("marketValue", Decimal128('0'))
                unrealized_gain_loss = holding.get("unrealizedGainLoss", Decimal128('0'))

                # Add to totals
                total_market_value = Decimal128(
                    str(total_market_value.to_decimal() + market_value.to_decimal())
                )
                unrealized_gain_loss_total = Decimal128(
                    str(unrealized_gain_loss_total.to_decimal() + unrealized_gain_loss.to_decimal())
                )
                continue

            # Calculate new values with proper rounding to 2 decimal places
            # First convert to Python Decimal for precise arithmetic
            close_price = Decimal(str(latest_trade.get("close")))
            qty = Decimal(str(quantity.to_decimal()))
            cost = Decimal(str(total_cost.to_decimal()))

            # Round to 2 decimal places
            last_traded_price_decimal = close_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            market_value_decimal = (qty * last_traded_price_decimal).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            unrealized_gain_loss_decimal = (market_value_decimal - cost).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

            # Convert back to Decimal128 for MongoDB storage
            last_traded_price = Decimal128(str(last_traded_price_decimal))
            market_value = Decimal128(str(market_value_decimal))
            unrealized_gain_loss = Decimal128(str(unrealized_gain_loss_decimal))

            # Update the holding
            updated_holding = holding.copy()
            updated_holding["lastTradedPrice"] = last_traded_price
            updated_holding["marketValue"] = market_value
            updated_holding["unrealizedGainLoss"] = unrealized_gain_loss

            updated_holdings.append(updated_holding)
            updated_holdings_count += 1

            # Add to totals
            total_market_value = Decimal128(
                str((Decimal(str(total_market_value.to_decimal())) + market_value_decimal).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
            )
            unrealized_gain_loss_total = Decimal128(
                str((Decimal(str(unrealized_gain_loss_total.to_decimal())) + unrealized_gain_loss_decimal).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
            )

        # Update the portfolio in the database
        update_result = await PortfolioRepository.update_portfolio_values(
            portfolio_id=portfolio_id,
            holdings=updated_holdings,
            total_market_value=total_market_value,
            total_unrealized_gain_loss=unrealized_gain_loss_total,
            last_updated_date=update_timestamp
        )

        if update_result:
            logger.info(f"Successfully updated portfolio {portfolio_id} with latest market data: "
                       f"{updated_holdings_count} holdings updated, {skipped_holdings_count} skipped")

            # Log portfolio value to portfolio_value_history collection for benchmarking
            await log_portfolio_value_history(
                portfolio_id=portfolio_id,
                total_market_value=total_market_value,
                update_date=update_timestamp
            )

            # Prepare detailed response
            response = {
                "success": True,
                "message": f"Portfolio {portfolio_id} updated successfully",
                "portfolioId": portfolio_id,
                "totalMarketValue": float(total_market_value.to_decimal()),
                "totalUnrealizedGainLoss": float(unrealized_gain_loss_total.to_decimal()),
                "lastUpdatedDate": update_timestamp.isoformat(),
                "updatedHoldingsCount": updated_holdings_count,
                "skippedHoldingsCount": skipped_holdings_count
            }

            # Add details about skipped holdings if any
            if holdings_with_no_data:
                response["holdingsWithNoData"] = holdings_with_no_data

            return response
        else:
            logger.error(f"Failed to update portfolio {portfolio_id} in database")
            return {
                "success": False,
                "message": f"Failed to update portfolio {portfolio_id} in database",
                "portfolioId": portfolio_id,
                "error_code": "DATABASE_UPDATE_FAILED"
            }

    except Exception as e:
        logger.error(f"Error updating portfolio {portfolio_id}: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"Error updating portfolio {portfolio_id}: {str(e)}",
            "portfolioId": portfolio_id,
            "error_code": "UPDATE_PROCESS_FAILED"
        }

async def manual_update_portfolios(portfolio_id: Optional[str] = None, force_update: bool = False) -> Dict[str, Any]:
    """
    Manually trigger portfolio updates.

    This function updates the following fields:
    - At portfolio level: totalMarketValue, totalUnrealizedGainLoss, lastUpdatedDate
    - At holding level: lastTradedPrice, marketValue, unrealizedGainLoss

    Args:
        portfolio_id: Optional specific portfolio ID to update. If None, updates all portfolios.
        force_update: If True, update even on weekends. Default is False.

    Returns:
        Dictionary containing update operation result with standardized format
    """
    update_timestamp = datetime.now(ZoneInfo("Asia/Colombo"))

    # Check if today is a weekend
    weekend_skip = skip_if_weekend(update_timestamp, "manual portfolio update", force=force_update)
    if weekend_skip:
        # Add service-specific fields to the standard weekend skip response
        weekend_skip.update({
            "portfolios_updated": 0,
            "portfolios_failed": 0
        })
        return weekend_skip

    try:
        if portfolio_id:
            # Update a specific portfolio
            logger.info(f"Manual update requested for portfolio {portfolio_id}")
            portfolio = await PortfolioRepository.get_portfolio(portfolio_id)

            if not portfolio:
                logger.warning(f"Portfolio with ID {portfolio_id} not found")
                return {
                    "success": False,
                    "message": f"Portfolio with ID {portfolio_id} not found",
                    "error_code": "PORTFOLIO_NOT_FOUND",
                    "portfolios_updated": 0,
                    "timestamp": update_timestamp.isoformat()
                }

            result = await update_portfolio(portfolio)

            if result.get("success"):
                # Create a standardized update detail entry
                update_detail = {
                    "portfolioId": portfolio_id,
                    "status": "updated",
                    "newMarketValue": result.get("totalMarketValue"),
                    "totalUnrealizedGainLoss": result.get("totalUnrealizedGainLoss"),
                    "lastUpdatedDate": result.get("lastUpdatedDate"),
                    "holdingsUpdated": result.get("updatedHoldingsCount", 0),
                    "holdingsSkipped": result.get("skippedHoldingsCount", 0)
                }

                # Include any holdings with no data if present
                if "holdingsWithNoData" in result:
                    update_detail["holdingsWithNoData"] = result["holdingsWithNoData"]

                return {
                    "success": True,
                    "message": f"Portfolio {portfolio_id} updated successfully",
                    "portfolios_updated": 1,
                    "update_details": [update_detail],
                    "timestamp": update_timestamp.isoformat()
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to update portfolio {portfolio_id}: {result.get('message')}",
                    "error_code": result.get("error_code", "UPDATE_FAILED"),
                    "portfolios_updated": 0,
                    "update_details": [result],
                    "timestamp": update_timestamp.isoformat()
                }
        else:
            # Update all portfolios
            logger.info("Manual update requested for all portfolios")
            return await update_all_portfolios(force=force_update)

    except Exception as e:
        logger.error(f"Error in manual portfolio update: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"Portfolio update process failed: {str(e)}",
            "error_code": "MANUAL_UPDATE_FAILED",
            "portfolios_updated": 0,
            "timestamp": update_timestamp.isoformat()
        }
