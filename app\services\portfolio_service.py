"""
Portfolio Service

This module provides functions for creating and managing portfolios.
"""

import logging
from typing import Dict, Optional, Any, Tuple, cast
from datetime import datetime

from app.core.optimisation_engine import OptimisationEngine
from app.core.portfolio_interpolation import interpolate_portfolios
from app.core.portfolio_validation import validate_target_return, handle_optimization_error
from app.services.company_service import get_sp_sl20_companies, get_company_sectors
from app.services.portfolio_storage_service import get_portfolio
from app.utils.type_convert import convert_decimal_to_float
from app.utils.error_handlers import APIError
from app.config import (
    get_default_constraints,
    get_portfolio_type_description,
    PORTFOLIO_TYPE_MIN_VARIANCE,
    PORTFOLIO_TYPE_MAX_SHARPE,
    PORTFOLIO_TYPE_CUSTOM,
    DEFAULT_START_DATE,
    DEFAULT_END_DATE
)

# Configure logging
logger = logging.getLogger(__name__)

async def _initialize_engine(
    companies_dict: Dict[str, str],
    constraints: Dict[str, Any]
) -> <PERSON><PERSON>[OptimisationEngine, Dict[str, Any]]:
    """
    Initialize the OptimisationEngine with the given companies and constraints.

    Args:
        companies_dict: Dictionary mapping company names to ticker symbols
        constraints: Dictionary containing constraints for portfolio optimization

    Returns:
        Tuple containing the initialized engine and the constraints used
    """
    # Get default constraints
    default_constraints = get_default_constraints()

    # Merge with provided constraints
    if constraints:
        for key, value in constraints.items():
            if value is not None:
                default_constraints[key] = value

    # Ensure date parameters are set
    from_date = default_constraints.get("from_date") or default_constraints.get("start_date")
    to_date = default_constraints.get("to_date") or default_constraints.get("end_date")

    # Initialize the OptimisationEngine
    engine = OptimisationEngine(
        companies_dict=companies_dict,
        from_date=from_date,
        to_date=to_date,
        risk_free_rate=default_constraints["risk_free_rate"],
        weight_bounds=default_constraints["weight_bounds"]
    )

    # Load the data (async)
    await engine.get_data()

    return engine, default_constraints

async def get_portfolio_optimization_results(
    custom_constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Get the complete portfolio optimization results including min variance, max sharpe, and efficient frontier.

    Args:
        custom_constraints: Optional custom constraints to override defaults

    Returns:
        Dictionary containing the complete portfolio optimization results
    """
    try:
        # Get default constraints
        constraints = get_default_constraints()

        # Override with custom constraints if provided
        if custom_constraints:
            constraints.update(custom_constraints)

        # Get S&PSL20 companies
        companies_dict = await get_sp_sl20_companies()

        if not companies_dict:
            raise APIError(
                detail="No S&PSL20 companies found in the database",
                status_code=404,
                error_code="PORTFOLIO_NOT_FOUND",
                explanation="The system could not find any S&PSL20 companies in the database.",
                recommendation="Please ensure the database is properly populated with company data."
            )

        # Initialize the engine
        engine, constraints = await _initialize_engine(companies_dict, constraints)

        # Get the portfolio optimization results
        portfolio_summary = engine.get_portfolio_summary()

        return portfolio_summary

    except Exception as e:
        logger.error("Error getting portfolio optimization results: %s", str(e))
        raise APIError(
            detail=f"Failed to get portfolio optimization results: {str(e)}",
            status_code=500,
            error_code="SERVER_ERROR",
            explanation="An unexpected error occurred while optimizing the portfolio.",
            recommendation="Please try again later or contact support if the problem persists."
        ) from e

async def _create_default_portfolio(
    portfolio_type: str,
    custom_constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a default portfolio (min variance or max sharpe) using S&PSL20 companies.

    Args:
        portfolio_type: Type of portfolio (PORTFOLIO_TYPE_MIN_VARIANCE or PORTFOLIO_TYPE_MAX_SHARPE)
        custom_constraints: Optional custom constraints to override defaults

    Returns:
        Dictionary containing portfolio details
    """
    try:
        # Get default constraints
        constraints = get_default_constraints()

        # Override with custom constraints if provided
        if custom_constraints:
            constraints.update(custom_constraints)

        # Get S&PSL20 companies
        companies_dict = await get_sp_sl20_companies()

        if not companies_dict:
            raise ValueError("No S&PSL20 companies found in the database")

        # Handle excluded companies if provided
        excluded_companies = custom_constraints.get("excluded_companies") if custom_constraints else None
        if excluded_companies:
            # Remove excluded companies from the companies dictionary
            for company_name, ticker in excluded_companies.items():
                if company_name in companies_dict:
                    del companies_dict[company_name]
                elif ticker in companies_dict.values():
                    # If company name not found, try to find by ticker
                    for name, sym in list(companies_dict.items()):
                        if sym == ticker:
                            del companies_dict[name]
                            break

            # Ensure we still have enough companies after exclusions
            if len(companies_dict) < 5:
                raise ValueError("After excluding companies, there are fewer than 5 companies remaining. Please exclude fewer companies.")

        # Initialize the engine
        engine, constraints = await _initialize_engine(companies_dict, constraints)

        # Get the portfolio optimization results
        portfolio_summary = engine.get_portfolio_summary()

        # Extract the appropriate portfolio based on type
        if portfolio_type == PORTFOLIO_TYPE_MIN_VARIANCE:
            portfolio_data = portfolio_summary["min_variance"]
            error_message = "Optimization failed to produce valid allocations for minimum variance portfolio. This may be due to insufficient historical data or constraints that are too restrictive."
        elif portfolio_type == PORTFOLIO_TYPE_MAX_SHARPE:
            portfolio_data = portfolio_summary["max_sharpe_ratio"]
            error_message = "Optimization failed to produce valid allocations for maximum Sharpe ratio portfolio. This may be due to insufficient historical data, constraints that are too restrictive, or an inappropriate risk-free rate."
        else:
            raise ValueError(f"Invalid portfolio type: {portfolio_type}")

        # Ensure we have allocations
        if not portfolio_data['allocations']:
            raise ValueError(error_message)

        # Log basic information
        logger.debug("Created %s portfolio with %d allocations", portfolio_type, len(portfolio_data['allocations']))

        # Create the response
        response = {
            "portfolio_type": portfolio_type,
            "description": get_portfolio_type_description(portfolio_type),
            "performance": portfolio_data["performance"],
            "allocations": portfolio_data["allocations"],
            "constraints_used": {
                "start_date": constraints["start_date"],
                "end_date": constraints["end_date"],
                "risk_free_rate": constraints["risk_free_rate"],
                "weight_bounds": constraints["weight_bounds"],
                "companies": list(companies_dict.keys()),
                "excluded_companies": list(excluded_companies.keys()) if excluded_companies else []
            }
        }

        return response

    except Exception as e:
        logger.error("Error creating %s portfolio: %s", portfolio_type, str(e))
        raise APIError(
            detail=f"Failed to create {portfolio_type} portfolio: {str(e)}",
            status_code=500,
            error_code="SERVER_ERROR",
            explanation="An unexpected error occurred while creating the portfolio.",
            recommendation="Please try again later or contact support if the problem persists."
        ) from e

async def create_min_variance_portfolio(
    custom_constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a minimum variance portfolio using S&PSL20 companies.

    Args:
        custom_constraints: Optional custom constraints to override defaults

    Returns:
        Dictionary containing portfolio details
    """

    created_portfolio = await _create_default_portfolio(PORTFOLIO_TYPE_MIN_VARIANCE, custom_constraints)

    return created_portfolio

async def create_max_sharpe_portfolio(
    custom_constraints: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a maximum Sharpe ratio portfolio using S&PSL20 companies.

    Args:
        custom_constraints: Optional custom constraints to override defaults

    Returns:
        Dictionary containing portfolio details
    """
    return await _create_default_portfolio(PORTFOLIO_TYPE_MAX_SHARPE, custom_constraints)

async def create_custom_portfolio(
    companies_dict: Dict[str, str],
    constraints: Dict[str, Any],
    target_return: float
) -> Dict[str, Any]:
    """
    Create a custom portfolio with user-specified companies and constraints.

    Args:
        companies_dict: Dictionary mapping company names to ticker symbols
        constraints: Dictionary containing constraints for portfolio optimization
        target_return: Target return for the portfolio (percentage)

    Returns:
        Dictionary containing portfolio details
    """
    try:
        if not companies_dict:
            raise APIError(
                detail="No companies specified for custom portfolio",
                status_code=400,
                error_code="MISSING_COMPANIES",
                explanation="At least 5 companies are required for optimal portfolio diversification.",
                recommendation="Please select at least 5 companies for your portfolio."
            )

        # Get default constraints
        default_constraints = get_default_constraints()

        # Merge with user-specified constraints
        for key, value in constraints.items():
            if value is not None:
                default_constraints[key] = value

        # Ensure date parameters are set
        if "from_date" not in default_constraints or default_constraints["from_date"] is None:
            default_constraints["from_date"] = DEFAULT_START_DATE
        if "to_date" not in default_constraints or default_constraints["to_date"] is None:
            default_constraints["to_date"] = DEFAULT_END_DATE

        # Initialize the engine
        engine, default_constraints = await _initialize_engine(companies_dict, default_constraints)

        # Ensure target return is in decimal form
        # Convert percentage to decimal if needed (e.g., 120.0 -> 1.2, 100.0 -> 1.0, 0.15 -> 0.15)
        target_return_decimal = target_return / 100.0 if target_return >= 1.0 else target_return

        # Log the conversion for debugging
        logger.debug(f"Target return conversion: {target_return} -> {target_return_decimal:.4f}")

        # Get the min variance and max sharpe portfolios to validate target return
        portfolio_summary = engine.get_portfolio_summary()

        # Get the returns from the portfolio summary
        min_variance_return_raw = portfolio_summary["min_variance"]["performance"]["returns"]
        max_sharpe_return_raw = portfolio_summary["max_sharpe_ratio"]["performance"]["returns"]

        # Convert to decimal form if needed
        # Portfolio summary returns are always in percentage format (>1.0), so convert to decimal
        min_variance_return = min_variance_return_raw / 100.0 if min_variance_return_raw >= 1.0 else min_variance_return_raw
        max_sharpe_return = max_sharpe_return_raw / 100.0 if max_sharpe_return_raw >= 1.0 else max_sharpe_return_raw

        # Log the values for debugging
        logger.debug(f"Min variance return: {min_variance_return_raw} -> {min_variance_return:.4f}")
        logger.debug(f"Max sharpe return: {max_sharpe_return_raw} -> {max_sharpe_return:.4f}")

        # Validate that the target return is within the efficient frontier
        is_valid, error_message = validate_target_return(
            target_return=target_return_decimal,
            min_variance_return=min_variance_return,
            max_sharpe_return=max_sharpe_return
        )

        if not is_valid:
            if error_message is None:
                raise APIError(
                    detail="Invalid target return",
                    status_code=422,
                    error_code="VALIDATION_ERROR",
                    explanation="The target return validation failed.",
                    recommendation="Please try a different target return."
                )

            if "below the minimum variance" in error_message:
                # Use the min_variance_return value we already have from the portfolio summary
                # Format it to 2 decimal places for display
                min_var_display = f"{min_variance_return*100:.2f}"

                # Format according to the specified error response
                raise APIError(
                    detail="Inefficient Target Return!",
                    status_code=422,
                    error_code="INEFFICIENT_TARGET_RETURN",
                    explanation="Your target return is below the lowest optimal target return for the specified companies and constraints",
                    recommendation=f"Increase your target return to {min_var_display}% or above"
                )
            else:
                # Use the max_sharpe_return value we already have from the portfolio summary
                # Format it to 2 decimal places for display
                max_sharpe_display = f"{max_sharpe_return*100:.2f}"

                # Format according to the specified error response
                raise APIError(
                    detail="Target Return Unachievable!",
                    status_code=422,
                    error_code="UNACHIEVABLE_TARGET_RETURN",
                    explanation="Your target return is above the maximum optimal target return for the specified companies and constraints",
                    recommendation=f"Decrease your target return to {max_sharpe_display}% or below"
                )

        # Try direct optimization for the target return first
        try:
            logger.info("Attempting direct optimization for target return %f", target_return_decimal)

            # Directly optimize for the target return
            result = engine.efficient_frontier(
                target_return=target_return_decimal,
                custom_bounds=None,
                custom_constraints=None
            )

            # Get the portfolio performance
            portfolio_return, portfolio_std = engine.portfolio_performance(result['x'])

            # Keep as decimals for internal calculations
            portfolio_return = float(round(portfolio_return, 4))
            portfolio_std = float(round(portfolio_std, 4))

            # Create weights dictionary
            weights = {}
            if engine.mean_returns is not None:
                mean_returns = cast(Any, engine.mean_returns)  # Type cast to handle index access
                if hasattr(mean_returns, 'index'):
                    for i, ticker in enumerate(mean_returns.index):
                        weight = float(round(result['x'][i], 4))  # Keep as decimal
                        if weight > 0.0001:  # Only include weights > 0.01%
                            weights[ticker] = weight
                else:
                    raise APIError(
                        detail="Engine mean returns data is not properly initialized",
                        status_code=500,
                        error_code="SERVER_ERROR",
                        explanation="The optimization engine's data structure is not properly initialized.",
                        recommendation="Please try again later or contact support if the problem persists."
                    )
            else:
                raise APIError(
                    detail="Engine mean returns data is not properly initialized",
                    status_code=500,
                    error_code="SERVER_ERROR",
                    explanation="The optimization engine's data structure is not properly initialized.",
                    recommendation="Please try again later or contact support if the problem persists."
                )

            # Create the optimized portfolio
            optimized_portfolio = {
                "volatility": portfolio_std,
                "return": portfolio_return,
                "weights": weights,
                "sharpe_ratio": (portfolio_return - default_constraints["risk_free_rate"]) / portfolio_std if portfolio_std > 0 else 0
            }

            logger.info("Successfully optimized portfolio with return %f and volatility %f", portfolio_return, portfolio_std)
            closest_portfolio = optimized_portfolio

        except Exception as e:
            # Get a user-friendly error message
            user_friendly_message = handle_optimization_error(e)
            logger.warning("Direct optimization failed: %s. Falling back to efficient frontier search.", user_friendly_message)

            # Fall back to searching the efficient frontier
            efficient_frontier = engine.get_efficient_frontier(
                num_points=20,
                min_target_return=target_return_decimal
            )

            # Try to interpolate a portfolio with the exact target return
            interpolated_portfolio = interpolate_portfolios(
                target_return=target_return_decimal,
                portfolios=efficient_frontier,
                tolerance=0.001
            )

            if interpolated_portfolio:
                logger.info("Successfully interpolated portfolio with return %f", interpolated_portfolio['return'])
                closest_portfolio = interpolated_portfolio
            else:
                # If interpolation fails, fall back to finding the closest portfolio
                logger.warning("Interpolation failed. Falling back to finding closest portfolio.")

                # Find the portfolio with the closest return to the target
                closest_portfolio = None
                min_diff = float('inf')

                for portfolio in efficient_frontier:
                    diff = abs(portfolio["return"] - target_return_decimal)
                    if diff < min_diff:
                        min_diff = diff
                        closest_portfolio = portfolio

                if closest_portfolio is None:
                    # Format max_sharpe_return to 2 decimal places for display
                    max_sharpe_display = f"{max_sharpe_return*100:.2f}"

                    raise APIError(
                        detail="Target Return Unachievable!",
                        status_code=422,
                        error_code="UNACHIEVABLE_TARGET_RETURN",
                        explanation="Your target return is above the maximum optimal target return for the specified companies and constraints",
                        recommendation=f"Decrease your target return to {max_sharpe_display}% or below"
                    ) from e

                logger.info("Found closest portfolio with return %f (target was %f)", closest_portfolio['return'], target_return_decimal)

        # Ensure we have sufficient allocations
        if not closest_portfolio['weights']:
            raise APIError(
                detail="Optimization failed to produce valid allocations",
                status_code=422,
                error_code="VALIDATION_ERROR",
                explanation="This may be due to insufficient companies, insufficient historical data, or constraints that are too restrictive.",
                recommendation="Try selecting more companies or relaxing the constraints."
            )

        if len(closest_portfolio['weights']) < 5:
            logger.warning("Portfolio has only %d assets, which may not provide optimal diversification", len(closest_portfolio['weights']))

        # Create the response
        response = {
            "portfolio_type": PORTFOLIO_TYPE_CUSTOM,
            "description": f"Custom Portfolio with target return {round(target_return_decimal*100, 2)}%",
            "performance": {
                "returns": round(float(closest_portfolio["return"]*100), 2),
                "volatility": round(float(closest_portfolio["volatility"]*100), 2),
                "sharpe_ratio": round(float((closest_portfolio["return"] - default_constraints["risk_free_rate"]) / closest_portfolio["volatility"]), 2)
            },
            "allocations": {
                ticker: round(float(weight*100), 2)
                for ticker, weight in closest_portfolio["weights"].items()
            },
            "constraints_used": {
                "start_date": default_constraints["start_date"],
                "end_date": default_constraints["end_date"],
                "risk_free_rate": default_constraints["risk_free_rate"],
                "weight_bounds": default_constraints["weight_bounds"],
                "target_return": round(target_return_decimal*100, 2),
                "companies": list(companies_dict.keys())
            }
        }

        return response

    except APIError as api_error:
        # Re-raise APIError exceptions directly without wrapping them
        logger.error("API Error in create_custom_portfolio: %s (Code: %s)", api_error.detail, api_error.error_code)
        raise api_error

    except Exception as e:
        logger.error("Error creating custom portfolio: %s", str(e))
        if "No trade data found for" in str(e) or "No common trading days" in str(e):
            raise APIError(
                detail=str(e),
                status_code=422,
                error_code="NO_COMMON_TRADING_DAYS",
                explanation="Not enough overlapping trade data found for the selected companies.",
                recommendation="Change the selected companies or the date range."
            ) from e
        raise APIError(
            detail=f"Failed to create custom portfolio: {str(e)}",
            status_code=500,
            error_code="SERVER_ERROR",
            explanation="An unexpected error occurred while creating the portfolio.",
            recommendation="Please try again later or contact support if the problem persists."
        ) from e

async def get_portfolio_sector_allocations(portfolio_id: str) -> Dict[str, Any]:
    """
    Calculate sector allocations for a specific portfolio.

    Args:
        portfolio_id: The ID of the portfolio to calculate sector allocations for

    Returns:
        Dictionary containing portfolio sector allocations

    Raises:
        ValueError: If the portfolio is not found or has no holdings
    """
    try:
        # Get the portfolio from the database
        portfolio = await get_portfolio(portfolio_id)

        if not portfolio:
            raise APIError(
                detail=f"Portfolio with ID {portfolio_id} not found",
                status_code=404,
                error_code="PORTFOLIO_NOT_FOUND",
                explanation="The portfolio could not be found in the database.",
                recommendation="Please check the portfolio ID and try again."
            )

        # Extract portfolio details
        portfolio_name = portfolio.get("portfolioName", "Unknown Portfolio")
        total_market_value = convert_decimal_to_float(portfolio.get("totalMarketValue", 0))
        holdings = portfolio.get("holdings", [])

        if not holdings:
            raise APIError(
                detail=f"Portfolio with ID {portfolio_id} has no holdings",
                status_code=400,
                error_code="INVALID_PORTFOLIO",
                explanation="The portfolio has no holdings.",
                recommendation="Please check the portfolio holdings and try again."
            )

        # Get ticker symbols from holdings
        ticker_symbols = [holding.get("tickerSymbol") for holding in holdings if holding.get("tickerSymbol")]

        if not ticker_symbols:
            raise APIError(
                detail=f"Portfolio with ID {portfolio_id} has no valid ticker symbols",
                status_code=400,
                error_code="INVALID_PORTFOLIO",
                explanation="The portfolio has no valid ticker symbols.",
                recommendation="Please check the portfolio holdings and try again."
            )

        # Get sector information for these ticker symbols
        sectors_dict = await get_company_sectors(ticker_symbols)

        if not sectors_dict:
            raise APIError(
                detail=f"Could not retrieve sector information for portfolio with ID {portfolio_id}",
                status_code=500,
                error_code="SERVER_ERROR",
                explanation="An unexpected error occurred while retrieving sector information.",
                recommendation="Please try again later or contact support if the problem persists."
            )

        # Calculate sector allocations
        sector_values = {}
        for holding in holdings:
            ticker = holding.get("tickerSymbol")
            if not ticker:
                continue

            market_value = convert_decimal_to_float(holding.get("marketValue", 0))
            sector = sectors_dict.get(ticker, "Unknown")

            if sector in sector_values:
                sector_values[sector] += market_value
            else:
                sector_values[sector] = market_value

        # Calculate percentages and create the response
        sector_allocations = []
        for sector, market_value in sector_values.items():
            allocation_percentage = (market_value / total_market_value) * 100 if total_market_value > 0 else 0
            sector_allocations.append({
                "sector": sector,
                "allocation_percentage": round(allocation_percentage, 2),
                "market_value": round(market_value, 2)
            })

        # Sort by allocation percentage in descending order
        sector_allocations.sort(key=lambda x: x["allocation_percentage"], reverse=True)

        return {
            "portfolio_id": portfolio_id,
            "portfolio_name": portfolio_name,
            "total_market_value": round(total_market_value, 2),
            "sector_allocations": sector_allocations
        }

    except Exception as e:
        logger.error("Error calculating sector allocations for portfolio %s: %s", portfolio_id, str(e))
        raise APIError(
            detail=f"Failed to calculate sector allocations for portfolio {portfolio_id}: {str(e)}",
            status_code=500,
            error_code="SERVER_ERROR",
            explanation="An unexpected error occurred while calculating sector allocations.",
            recommendation="Please try again later or contact support if the problem persists."
        ) from e

async def get_portfolio_performance_history(
    portfolio_id: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get the performance history (market value over time) for a specific portfolio.

    Args:
        portfolio_id: The ID of the portfolio to get performance history for
        from_date: Optional start date in YYYY-MM-DD format (defaults to portfolio creation date)
        to_date: Optional end date in YYYY-MM-DD format (defaults to current date)

    Returns:
        Dictionary containing portfolio performance history data

    Raises:
        ValueError: If the portfolio is not found or has no performance history
    """
    try:
        # Get the portfolio from the database
        portfolio = await get_portfolio(portfolio_id)

        if not portfolio:
            raise ValueError(f"Portfolio with ID {portfolio_id} not found")

        # Extract portfolio details
        portfolio_name = portfolio.get("portfolioName", "Unknown Portfolio")
        initial_investment = convert_decimal_to_float(portfolio.get("totalInvestmentAmount", 0))
        creation_date = portfolio.get("creationDate")

        # If from_date is not provided, use the portfolio creation date
        if not from_date and creation_date:
            from_date = creation_date.strftime("%Y-%m-%d")
        elif not from_date:
            from_date = DEFAULT_START_DATE

        # If to_date is not provided, use current date
        if not to_date:
            to_date = datetime.now().strftime("%Y-%m-%d")

        # Import here to avoid circular imports
        from app.services.benchmark_service import get_portfolio_value_history

        # Ensure we have valid string dates
        from_date_str = str(from_date) if from_date else DEFAULT_START_DATE
        to_date_str = str(to_date) if to_date else datetime.now().strftime("%Y-%m-%d")

        # Get the portfolio value history
        history = await get_portfolio_value_history(portfolio_id, from_date_str, to_date_str)

        if not history:
            raise ValueError(f"No performance history found for portfolio {portfolio_id} in the specified date range")

        # Format the performance data
        performance_data = []
        for record in history:
            # Handle date formatting safely
            date_str = record.get("dateStr")
            if not date_str and record.get("date"):
                date_obj = record["date"]
                if isinstance(date_obj, datetime):
                    date_str = date_obj.strftime("%Y-%m-%d")
                else:
                    date_str = str(date_obj)

            if not date_str:
                continue  # Skip records without valid dates

            market_value = convert_decimal_to_float(record.get("totalMarketValue", 0))

            performance_data.append({
                "date": date_str,
                "market_value": round(market_value, 2)
            })

        # Sort by date
        performance_data.sort(key=lambda x: x["date"])

        # Determine the actual date range used
        date_range = {
            "from_date": performance_data[0]["date"] if performance_data else from_date_str,
            "to_date": performance_data[-1]["date"] if performance_data else to_date_str
        }

        return {
            "portfolio_id": portfolio_id,
            "portfolio_name": portfolio_name,
            "initial_investment": round(initial_investment, 2),
            "performance_data": performance_data,
            "date_range": date_range
        }

    except Exception as e:
        logger.error("Error retrieving performance history for portfolio %s: %s", portfolio_id, str(e))
        raise APIError(
            detail=f"Failed to retrieve performance history for portfolio {portfolio_id}: {str(e)}",
            status_code=500,
            error_code="SERVER_ERROR",
            explanation="An unexpected error occurred while retrieving the performance history.",
            recommendation="Please try again later or contact support if the problem persists."
        ) from e

