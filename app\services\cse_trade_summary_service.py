"""
CSE Trade Summary Service

This module handles fetching and storing daily
trade summary data from the Colombo Stock Exchange API.
It provides functionality for the daily scheduled data fetch.

Note: The CSE's tradeSummary API is a free public API that doesn't require authentication.
"""

import logging
import asyncio
import random
from datetime import datetime, time
from typing import Dict, Any, Optional
from decimal import Decimal
import uuid
import httpx
from bson.decimal128 import Decimal128
from fastapi import HTT<PERSON>Exception

# Import from new structure
from app.db.session import database
from app.db.repositories.trade_repository import TradeRepository
from app.models.trade_models import TopGainersLosers, TopGainersLosersResponse, TradesResponse, MostActiveTrade
from app.utils.db_utils import safe_mongodb_operation
from app.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# MongoDB Collections
COMPANY_COLLECTION = None  # Will be initialized when database is available
TRADES_COLLECTION = None  # Will be initialized when database is available

# API Endpoint from settings
DAILY_TRADE_SUMMARY_API_URL = settings.CSE_API_DAILY_SUMMARY_DATA_URL

# Initialize collections when database is ready
async def initialize_collections():
    global COMPANY_COLLECTION, TRADES_COLLECTION
    if database is not None:
        COMPANY_COLLECTION = database["companies"]
        TRADES_COLLECTION = database["trades"]
        logger.info("MongoDB collections initialized successfully")
    else:
        logger.error("Failed to initialize MongoDB collections: database is None")


async def fetch_with_retry(client: httpx.AsyncClient, url: str, params: Dict[str, Any], max_retries: int = 3, base_delay: float = 1.0) -> Dict:
    """Fetch data with exponential backoff retry logic.

    Args:
        client: httpx AsyncClient instance
        url: API endpoint URL
        params: Request parameters
        max_retries: Maximum number of retry attempts
        base_delay: Base delay for exponential backoff in seconds

    Returns:
        API response as dictionary

    Raises:
        Exception: If all retry attempts fail
    """
    retries = 0
    last_exception = None

    while retries <= max_retries:
        try:
            response = await client.post(url, params=params, headers={"Content-Type": "application/json"})

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:  # Too Many Requests
                # Rate limit hit, need to wait longer
                delay = base_delay * (2 ** retries) + (0.1 * random.random())
                logger.warning("Rate limit hit, retrying in %.2f seconds...", delay)
                await asyncio.sleep(delay)
                retries += 1
                continue
            else:
                # Other error status codes
                error_msg = f"API request failed with status {response.status_code}: {response.text}"
                logger.error(error_msg)
                last_exception = Exception(error_msg)
                break

        except Exception as e:
            last_exception = e
            delay = base_delay * (2 ** retries)
            logger.error("Request failed (attempt %d/%d): %s. Retrying in %.2f seconds...",
                        retries+1, max_retries+1, str(e), delay)
            await asyncio.sleep(delay)
            retries += 1

    if last_exception:
        raise last_exception
    return {}


def safe_int32(value):
    """Safely convert a value to Int32.

    Args:
        value: The value to convert

    Returns:
        The integer value if conversion is successful, None otherwise
    """
    if value is None:
        return None
    try:
        return int(value)  # MongoDB will store this as Int32 if within range
    except (ValueError, TypeError):
        logger.warning("Failed to convert %r to Int32", value)
        return None


def safe_decimal128(value):
    """Safely convert a value to Decimal128 for high precision.

    Args:
        value: The value to convert

    Returns:
        The Decimal128 value if conversion is successful, None otherwise
    """
    if value is None:
        return None
    try:
        # First convert to Decimal for exact decimal representation
        decimal_value = Decimal(str(value))
        # Then convert to MongoDB's Decimal128 type
        return Decimal128(decimal_value)
    except (ValueError, TypeError, Exception) as e:
        logger.warning("Failed to convert %r to Decimal128: %s", value, str(e))
        return None


async def get_symbol_for_security_id(security_id):
    """Get the ticker symbol for a given security ID.

    Args:
        security_id: The security ID to look up

    Returns:
        The ticker symbol if found, None otherwise
    """
    if security_id is None:
        return None

    try:
        if COMPANY_COLLECTION is None:
            logger.warning("Company collection not initialized")
            return None
        company = await safe_mongodb_operation(
            lambda: COMPANY_COLLECTION.find_one({"SECURITY_ID": security_id}) if COMPANY_COLLECTION else None,
            fallback_value=None
        )
        if company:
            return company.get("TICKER_SYMBOL") if isinstance(company, dict) else None
    except Exception as e:
        logger.warning("Failed to find symbol for security ID %s: %s", security_id, str(e))

    return None

# Fetch and store daily trade summary to the database
async def fetch_and_store_trade_summary(force_fetch: bool = False, specific_date: Optional[datetime] = None):
    """Fetch and store daily trade summary data from CSE API.

    Args:
        force_fetch: If True, fetch data even on weekends or if data already exists
        specific_date: Optional specific date to fetch data for (only used for historical data fetching)

    Returns:
        Dict with operation result message
    """
    current_date = specific_date if specific_date else datetime.now()
    current_day = current_date.strftime("%Y-%m-%d")
    today = datetime.now()

    # Compare dates by year, month, and day only
    is_today = (
        current_date.year == today.year and
        current_date.month == today.month and
        current_date.day == today.day
    )

    # If specific_date is provided and it's not today, and force_fetch is False, skip
    if specific_date and not force_fetch:
        if not is_today:
            logger.info("Skipping trade data fetch for %s as it's not today's data", current_day)
            return {"message": f"Skipped trade data fetch for {current_day} (not today's data)", "is_trading_day": False}

    # If no specific_date is provided but current_day is not today, skip
    if not specific_date and not is_today:
        logger.info("Skipping trade data fetch for %s as it's not today's data", current_day)
        return {"message": f"Skipped trade data fetch for {current_day} (not today's data)", "is_trading_day": False}

    # Check if today is a weekend (Saturday=5, Sunday=6)
    is_weekend = current_date.weekday() >= 5
    if is_weekend and not force_fetch:
        logger.info("Skipping trade data fetch for %s as it's a weekend", current_day)
        return {"message": f"Skipped trade data fetch for {current_day} (weekend)", "is_trading_day": False}

    # Check if we already have data for the specified date
    try:
        # Check if any records exist for the date
        existing_records = await TradeRepository.check_trade_data_exists(current_date)

        if existing_records > 0 and not force_fetch:
            logger.info("Skipping trade data fetch for %s as data already exists (%d records)",
                    current_day, existing_records)
            return {"message": f"Skipped trade data fetch for {current_day} (data already exists)", "is_trading_day": True}

    except Exception as e:
        logger.error("Error checking for existing records: %s", str(e))
        # Continue with fetch attempt even if check fails

    # Proceed with fetching data
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "headers": {
                    "normalizedNames": {},
                    "lazyUpdate": None
                }
            }

            # Set content type header (no token needed for public API)
            headers = {
                "Content-Type": "application/json"
            }

            logger.info("Fetching trade summary data for %s", current_day)
            response = await client.post(DAILY_TRADE_SUMMARY_API_URL, json=payload, headers=headers)

            if response.status_code == 200:
                data = response.json().get("reqTradeSummery", [])

                # Since the API always returns data for the last trading day,
                # we need to check the actual trade date in the response
                if not data:
                    logger.warning("Unexpected empty response from trade summary API for %s", current_day)
                    return {"message": f"Unexpected empty response from API for {current_day}", "is_trading_day": None, "error": True}

                # Process and store the data
                mapped_data_list = []
                for item in data:
                    trade_date = datetime.fromtimestamp(item.get("lastTradedTime") / 1000) if item.get("lastTradedTime") else current_date
                    security_id = item.get("id")

                    # Create a unique ID based on security ID and trade date
                    unique_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{security_id}_{trade_date.strftime('%Y-%m-%d')}"))

                    mapped_data = {
                        "_id": unique_id,
                        "symbol": item.get("symbol"),
                        "securityId": safe_int32(security_id),
                        "id": safe_decimal128(item.get("id")),
                        "open": safe_decimal128(item.get("open")),
                        "high": safe_decimal128(item.get("high")),
                        "low": safe_decimal128(item.get("low")),
                        "close": safe_decimal128(item.get("closingPrice")),
                        "turnover": safe_decimal128(item.get("turnover")),
                        "shareVolume": safe_decimal128(item.get("sharevolume")),
                        "tradeVolume": safe_int32(item.get("tradevolume")),
                        "tradeDate": trade_date
                    }
                    mapped_data_list.append(mapped_data)

                # Use bulk upsert to replace existing data
                logger.info(f"Attempting to store {len(mapped_data_list)} trade records for {current_day}")
                success = await TradeRepository.bulk_upsert_trade_data(mapped_data_list)

                if success:
                    records_inserted = len(mapped_data_list)
                    logger.info("Trade summary data fetched and stored successfully. Updated %d records for trade date %s.",
                            records_inserted, current_day)
                    return {
                        "message": f"Trade summary data fetched and stored successfully",
                        "records_inserted": records_inserted,
                        "total_records": len(data),
                        "is_trading_day": True,
                        "trade_date": current_day
                    }
                else:
                    logger.error("Failed to store trade summary data for %s", current_day)
                    return {
                        "message": f"Failed to store trade summary data for {current_day}",
                        "records_inserted": 0,
                        "total_records": len(data),
                        "is_trading_day": True,
                        "trade_date": current_day,
                        "error": True
                    }
            else:
                error_msg = f"Failed to fetch trade summary: {response.status_code}, {response.text}"
                logger.error(error_msg)
                return {"message": error_msg, "is_trading_day": None, "error": True}
    except Exception as e:
        error_msg = f"Exception during trade summary fetch: {str(e)}"
        logger.error(error_msg)
        return {"message": error_msg, "is_trading_day": None, "error": True}

async def get_trades(trade_date: datetime, page: int = 1, limit: int = 50) -> TradesResponse:
    """Get the trade summary for a given date.

    Args:
        trade_date: The date to get the trade summary for
        page: Page number for pagination (default: 1)
        limit: Number of items per page (default: 50)

    Returns:
        TradesResponse with trade summary data and pagination info
    """
    try:
        start_of_day = datetime.combine(trade_date.date(), time.min)
        end_of_day = datetime.combine(trade_date.date(), time.max)
        today = datetime.now()

        # Compare dates by year, month, and day only
        is_today = (
            trade_date.year == today.year and
            trade_date.month == today.month and
            trade_date.day == today.day
        )

        # Check if data exists for the requested date
        total = await TradeRepository.check_trade_data_exists(trade_date)

        # Only try to fetch from API if:
        # 1. It's today's date OR
        # 2. It's a past date with no data
        if total == 0 and is_today:
            # Try to fetch data from API
            result = await fetch_and_store_trade_summary(force_fetch=True, specific_date=trade_date)
            if result.get("error"):
                logger.error("Error fetching from API: %s", result.get("message"))
                raise HTTPException(status_code=500, detail=result.get("message", "Failed to fetch trade data"))

            # Check again after fetching
            total = await TradeRepository.check_trade_data_exists(trade_date)
            if total == 0:
                logger.warning("No data available after API fetch")
                return TradesResponse(
                    data=[],
                    total=0,
                    page=page,
                    limit=limit,
                    totalPages=0,
                    hasNext=False,
                    hasPrevious=False
                )

        # Calculate pagination parameters
        skip = (page - 1) * limit
        total_pages = (total + limit - 1) // limit if total > 0 else 0

        # Query database for trades on the specified date
        query = {"tradeDate": {"$gte": start_of_day, "$lte": end_of_day}}
        trade_repo = TradeRepository()
        trades = await trade_repo.get_trades(
            query=query,
            sort=("symbol", 1),
            skip=skip,
            limit=limit
        )

        if not trades:
            return TradesResponse(
                data=[],
                total=total,
                page=page,
                limit=limit,
                totalPages=total_pages,
                hasNext=page < total_pages,
                hasPrevious=page > 1
            )

        # Helper functions to convert values
        def to_float(val):
            if isinstance(val, Decimal128):
                return float(val.to_decimal())
            return float(val or 0)

        def to_int(val):
            if isinstance(val, Decimal128):
                return int(val.to_decimal())
            return int(val or 0)

        # Process trade data
        trade_list = []
        for trade in trades:
            if trade is None:
                continue
            trade_data = {
                "tradeDate": trade.get("tradeDate", trade_date).strftime("%Y-%m-%d"),
                "symbol": str(trade.get("symbol", "")),
                "tradeVolume": to_int(trade.get("tradeVolume", 0)),
                "open": to_float(trade.get("open", 0)),
                "low": to_float(trade.get("low", 0)),
                "close": to_float(trade.get("close", 0)),
                "shareVolume": to_int(trade.get("shareVolume", 0)),
                "securityId": to_int(trade.get("securityId", 0)),
                "high": to_float(trade.get("high", 0)),
                "id": to_int(trade.get("id", 0)),
                "turnover": to_float(trade.get("turnover", 0))
            }
            trade_list.append(trade_data)

        return TradesResponse(
            data=trade_list,
            total=total,
            page=page,
            limit=limit,
            totalPages=total_pages,
            hasNext=page < total_pages,
            hasPrevious=page > 1
        )
    except Exception as e:
        logger.error("Error getting trade summary: %s", str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e)) from e

# Cache for most active trades
_most_active_trades_cache = {
    "data": None,
    "timestamp": None
}

async def get_most_active_trades() -> list[MostActiveTrade]:
    """Get the most active trades for the current date.

    Returns:
        List[MostActiveTrade]: List of most active trades
    """
    try:
        # Check if we have cached data from today
        current_time = datetime.now()
        cache = _most_active_trades_cache

        # Use cache if it exists and is from today (less than 15 minutes old)
        if (cache["data"] is not None and cache["timestamp"] is not None and
                cache["timestamp"].date() == current_time.date() and
                (current_time - cache["timestamp"]).seconds < 900):  # 15 minutes in seconds
            logger.info("Returning cached most active trades data")
            return cache["data"]

        # If no valid cache, fetch from API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                settings.CSE_API_MOST_ACTIVE_TRADES_URL,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code != 200:
                logger.error("Error from CSE API: %s - %s", response.status_code, response.text)
                raise HTTPException(
                    status_code=500,
                    detail="Failed to fetch most active trades from CSE"
                    )

            # Parse and validate the response data
            raw_data = response.json()
            validated_trades = [MostActiveTrade(**trade) for trade in raw_data]

            # Update cache
            _most_active_trades_cache.update({
                "data": validated_trades,
                "timestamp": current_time
            })
            logger.info("Updated most active trades cache")

            return validated_trades
    except Exception as e:
        logger.error("Error getting most active trades: %s", str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e)) from e

async def get_top_gainers_losers(date: str) -> TopGainersLosersResponse:
    """Get the top gainers and losers for a specific date.

    Args:
        date: The date to get the top gainers and losers for in YYYY-MM-DD format

    Returns:
        TopGainersLosersResponse: Object containing lists of top gainers and losers
    """
    try:
        gainers = []
        losers = []

        async with httpx.AsyncClient() as client:
            # Fetch gainers
            try:
                gainers_response = await client.post(settings.CSE_API_TOP_GAINERS_URL)
                if gainers_response.status_code == 200:
                    gainers_data = gainers_response.json()
                    gainers = [TopGainersLosers(**gainer) for gainer in gainers_data]
                else:
                    logger.warning("Failed to fetch top gainers: %s", gainers_response.status_code)
            except Exception as e:
                logger.error("Error fetching top gainers: %s", str(e))

            # Fetch losers
            try:
                losers_response = await client.post(settings.CSE_API_TOP_LOSERS_URL)
                if losers_response.status_code == 200:
                    losers_data = losers_response.json()
                    losers = [TopGainersLosers(**loser) for loser in losers_data]
                else:
                    logger.warning("Failed to fetch top losers: %s", losers_response.status_code)
            except Exception as e:
                logger.error("Error fetching top losers: %s", str(e))

        # Return partial results if we have at least one successful response
        if gainers or losers:
            return TopGainersLosersResponse(
                gainers=gainers,
                losers=losers
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to fetch both top gainers and losers from CSE"
            )

    except Exception as e:
        logger.error("Error getting top gainers and losers: %s", str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e)) from e