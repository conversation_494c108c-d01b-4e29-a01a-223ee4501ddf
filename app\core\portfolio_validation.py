"""
Portfolio Validation

This module provides validation functions for portfolio constraints and parameters.
"""

import logging
from typing import Tuple, Optional

# Configure logging
logger = logging.getLogger(__name__)

def validate_target_return(
    target_return: float,
    min_variance_return: float,
    max_sharpe_return: float,
    tolerance: float = 0.005
) -> Tuple[bool, Optional[str]]:
    """
    Validate that a target return is achievable within the efficient frontier.

    Args:
        target_return: The target return as a decimal (e.g., 0.15 for 15%)
        min_variance_return: The return of the minimum variance portfolio
        max_sharpe_return: The return of the maximum Sharpe ratio portfolio
        tolerance: Tolerance for comparing returns (default: 0.5%)

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Ensure all values are in decimal form (0-1 range)
    # Convert percentage to decimal if needed (e.g., 32.5 -> 0.325, 100.0 -> 1.0)
    target_return_decimal = target_return / 100.0 if target_return >= 1.0 else target_return
    min_variance_decimal = min_variance_return / 100.0 if min_variance_return >= 1.0 else min_variance_return
    max_sharpe_decimal = max_sharpe_return / 100.0 if max_sharpe_return >= 1.0 else max_sharpe_return

    # Log the values for debugging
    logger.debug(f"Target return: {target_return} -> {target_return_decimal:.4f}")
    logger.debug(f"Min variance return: {min_variance_return} -> {min_variance_decimal:.4f}")
    logger.debug(f"Max sharpe return: {max_sharpe_return} -> {max_sharpe_decimal:.4f}")

    # Check for unrealistic returns (over 100% in decimal form)
    # This is likely due to insufficient data points
    if min_variance_decimal > 1.0 or max_sharpe_decimal > 1.0:
        return False, (
            f"The calculated portfolio returns are unrealistically high (min variance: {min_variance_decimal*100:.2f}%, "
            f"max Sharpe: {max_sharpe_decimal*100:.2f}%). This is likely due to insufficient historical data. "
            f"Please select companies with more overlapping trading history or use a longer date range."
        )

    # Add tolerance to the range to allow for slight variations
    min_return = min_variance_decimal - tolerance
    max_return = max_sharpe_decimal + tolerance

    # Check if target return is below the minimum variance return
    if target_return_decimal < min_return:
        return False, (
            f"Target return of {target_return_decimal*100:.2f}% is below the minimum variance "
            f"portfolio return of {min_variance_decimal*100:.2f}%. The minimum variance portfolio "
            f"already provides the lowest possible risk."
        )

    # Check if target return is above the maximum Sharpe ratio return
    if target_return_decimal > max_return:
        return False, (
            f"Target return of {target_return_decimal*100:.2f}% is above the maximum Sharpe ratio "
            f"portfolio return of {max_sharpe_decimal*100:.2f}%. This return is not achievable "
            f"with the selected companies and constraints."
        )

    # Target return is within the efficient frontier
    return True, None

def validate_weight_constraints(min_weight: float, max_weight: float) -> Tuple[bool, Optional[str]]:
    """
    Validate weight constraints for portfolio optimization.

    Args:
        min_weight: Minimum weight constraint
        max_weight: Maximum weight constraint

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check if min_weight is negative
    if min_weight < 0:
        return False, "Minimum weight cannot be negative."

    # Check if max_weight exceeds 1
    if max_weight > 1:
        return False, "Maximum weight cannot exceed 1.0 (100%)."

    # Check if min_weight is greater than max_weight
    if min_weight > max_weight:
        return False, "Minimum weight cannot be greater than maximum weight."

    # Check if constraints are too restrictive
    if min_weight > 0.5:
        return False, (
            f"Minimum weight of {min_weight:.2%} is too high for proper diversification. "
            f"Consider using a lower minimum weight."
        )

    # Constraints are valid
    return True, None

def handle_optimization_error(error: Exception) -> str:
    """
    Handle optimization errors and return user-friendly error messages.

    Args:
        error: The exception raised during optimization

    Returns:
        User-friendly error message
    """
    error_str = str(error).lower()

    # Check for specific error patterns
    if "common trading days" in error_str:
        return str(error)  # Return the original error message as it's already user-friendly

    if "singular matrix" in error_str:
        return (
            "Optimization failed due to insufficient or highly correlated data. "
            "Try selecting different companies or a longer date range."
        )

    if "positive directional derivative" in error_str:
        return (
            "The target return is too high for the selected companies and constraints. "
            "Try a lower target return or select different companies."
        )

    if "negative directional derivative" in error_str:
        return (
            "The target return is too low for the selected companies and constraints. "
            "Try a higher target return or select different companies."
        )

    if "infeasible" in error_str:
        return (
            "The optimization problem is infeasible with the given constraints. "
            "Try relaxing the constraints or selecting different companies."
        )

    # Generic error message
    return (
        "Unable to create a portfolio with the specified parameters. "
        "Please try adjusting your constraints or selecting different companies."
    )
